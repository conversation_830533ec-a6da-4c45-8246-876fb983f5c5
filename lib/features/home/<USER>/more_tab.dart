import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:bill_book/core/widgets/app_text.dart';
import 'package:bill_book/core/router/app_routes.dart';

class MoreTab extends StatefulWidget {
  const MoreTab({super.key});

  @override
  State<MoreTab> createState() => _MoreTabState();
}

class _MoreTabState extends State<MoreTab> with TickerProviderStateMixin {
  late AnimationController _progressController;
  late AnimationController _fadeController;
  late Animation<double> _progressAnimation;
  late Animation<int> _counterAnimation;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();

    // Initialize animation controllers
    _progressController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    // Progress animation with smooth easing
    _progressAnimation = Tween<double>(begin: 0.0, end: 0.4).animate(
      CurvedAnimation(parent: _progressController, curve: Curves.easeOutCubic),
    );

    // Counter animation for percentage text
    _counterAnimation = IntTween(begin: 0, end: 40).animate(
      CurvedAnimation(parent: _progressController, curve: Curves.easeOutCubic),
    );

    // Fade in animation
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _fadeController, curve: Curves.easeOut));

    // Scale animation for entry effect
    _scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeOutBack),
    );

    // Start animations with a slight delay to coordinate with staggered animations
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        _fadeController.forward();
        Future.delayed(const Duration(milliseconds: 300), () {
          if (mounted) {
            _progressController.forward();
          }
        });
      }
    });
  }

  @override
  void dispose() {
    _progressController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      body: Stack(
        children: [
          SafeArea(
            child: SingleChildScrollView(
              padding: const EdgeInsets.fromLTRB(16, 16, 16, 80),
              child: AnimationLimiter(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: AnimationConfiguration.toStaggeredList(
                    duration: const Duration(milliseconds: 400),
                    delay: const Duration(milliseconds: 100),
                    childAnimationBuilder: (widget) => SlideAnimation(
                      verticalOffset: 30.0,
                      curve: Curves.easeOutCubic,
                      child: FadeInAnimation(
                        curve: Curves.easeOutCubic,
                        child: widget,
                      ),
                    ),
                    children: [
                      // Business Profile Header
                      AnimationConfiguration.staggeredList(
                        position: 0,
                        duration: const Duration(milliseconds: 350),
                        child: SlideAnimation(
                          horizontalOffset: -30.0,
                          curve: Curves.easeOutCubic,
                          child: FadeInAnimation(
                            curve: Curves.easeOutCubic,
                            child: _buildBusinessProfileHeader(),
                          ),
                        ),
                      ),
                      const SizedBox(height: 10),

                      // Complete Business Profile Card (already has animations)
                      _buildBusinessProfileCard(),
                      const SizedBox(height: 24),

                      // myBillBook Subscription
                      _buildAnimatedMenuTile(
                        position: 2,
                        icon: Icons.star,
                        iconColor: Colors.orange,
                        title: 'myBillBook Subscription Plan',
                        onTap: () {},
                      ),
                      const SizedBox(height: 8),

                      // Help
                      _buildAnimatedMenuTile(
                        position: 3,
                        icon: Icons.help_outline,
                        iconColor: const Color(0xFF5A67D8),
                        title: 'Help',
                        onTap: () {},
                      ),
                      const SizedBox(height: 8),

                      // Invite & Earn
                      _buildAnimatedMenuTile(
                        position: 4,
                        icon: Icons.card_giftcard,
                        iconColor: const Color(0xFF5A67D8),
                        title: 'Invite & Earn',
                        onTap: () {},
                      ),
                      const SizedBox(height: 24),

                      // Settings Section
                      _buildAnimatedSectionHeader('Settings', 5),
                      const SizedBox(height: 12),

                      _buildAnimatedMenuTile(
                        position: 6,
                        icon: Icons.receipt_long,
                        iconColor: const Color(0xFF5A67D8),
                        title: 'Invoice Settings',
                        badge: 'NEW',
                        onTap: () {},
                      ),
                      const SizedBox(height: 8),

                      _buildAnimatedMenuTile(
                        position: 7,
                        icon: Icons.person_outline,
                        iconColor: const Color(0xFF5A67D8),
                        title: 'Account Settings',
                        onTap: () {
                          HapticFeedback.lightImpact();
                          Navigator.pushNamed(
                            context,
                            AccountSettingsRoute.name,
                          );
                        },
                      ),
                      const SizedBox(height: 8),

                      _buildAnimatedMenuTile(
                        position: 8,
                        icon: Icons.notifications_outlined,
                        iconColor: const Color(0xFF5A67D8),
                        title: 'Reminder Settings',
                        onTap: () {},
                      ),
                      const SizedBox(height: 8),

                      _buildAnimatedMenuTile(
                        position: 9,
                        icon: Icons.manage_accounts_outlined,
                        iconColor: const Color(0xFF5A67D8),
                        title: 'Manage User',
                        onTap: () {},
                      ),
                      const SizedBox(height: 8),

                      _buildAnimatedMenuTile(
                        position: 10,
                        icon: Icons.restore_from_trash_outlined,
                        iconColor: const Color(0xFF5A67D8),
                        title: 'Recover Deleted Invoices',
                        onTap: () {},
                      ),
                      const SizedBox(height: 24),

                      // Others Section
                      _buildAnimatedSectionHeader('Others', 11),
                      const SizedBox(height: 12),

                      _buildAnimatedMenuTile(
                        position: 12,
                        icon: Icons.search,
                        iconColor: const Color(0xFF5A67D8),
                        title: 'GST Rate Finder',
                        onTap: () {},
                      ),
                      const SizedBox(height: 8),

                      _buildAnimatedMenuTile(
                        position: 13,
                        icon: Icons.print_outlined,
                        iconColor: const Color(0xFF5A67D8),
                        title: 'Buy Printer',
                        onTap: () {},
                      ),
                      const SizedBox(height: 8),

                      _buildAnimatedMenuTile(
                        position: 14,
                        icon: Icons.star_outline,
                        iconColor: const Color(0xFF5A67D8),
                        title: 'Rate app on Playstore',
                        onTap: () {},
                      ),
                      const SizedBox(height: 8),

                      _buildAnimatedMenuTile(
                        position: 15,
                        icon: Icons.info_outline,
                        iconColor: const Color(0xFF5A67D8),
                        title: 'About',
                        onTap: () {},
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),

          // Floating Help Button - positioned above bottom navigation
          Positioned(
            bottom: 10,
            right: 20,
            child: AnimationConfiguration.staggeredList(
              position: 16,
              duration: const Duration(milliseconds: 400),
              child: SlideAnimation(
                horizontalOffset: 50.0,
                curve: Curves.easeOutCubic,
                child: FadeInAnimation(
                  curve: Curves.easeOutCubic,
                  child: ScaleAnimation(
                    scale: 0.8,
                    curve: Curves.easeOutBack,
                    child: _buildFloatingHelpButton(),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBusinessProfileHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,

        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const AppText(
                  'Business Name',
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
                const SizedBox(height: 4),
                GestureDetector(
                  onTap: () {},
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.grey.shade300),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.05),
                          blurRadius: 2,
                          offset: const Offset(0, 1),
                        ),
                      ],
                    ),
                    child: const Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        AppText(
                          'BUSINESS & GST SETTINGS',
                          fontSize: 10,
                          fontWeight: FontWeight.w500,
                          color: Colors.black54,
                        ),
                        Icon(
                          Icons.arrow_forward_ios,
                          size: 12,
                          color: Colors.black54,
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(width: 16),
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: const Color(0xFF87CEEB),
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: const Center(
              child: AppText(
                'B',
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBusinessProfileCard() {
    return AnimatedBuilder(
      animation: _fadeController,
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeAnimation,
          child: Transform.translate(
            offset: Offset(0, 20 * (1 - _fadeAnimation.value)),
            child: child,
          ),
        );
      },
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          gradient: const LinearGradient(
            colors: [Color(0xFF5A67D8), Color(0xFF7C4DFF)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: const Color(0xFF5A67D8).withValues(alpha: 0.3),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: IntrinsicHeight(
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // First column: Text and Button
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const AppText(
                      'Complete business profile',
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                    const SizedBox(height: 12),
                    GestureDetector(
                      onTap: () {},
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 8,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(5),
                        ),
                        child: const Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            AppText(
                              'Add Details',
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                              color: Colors.white,
                            ),
                            SizedBox(width: 4),
                            Icon(
                              Icons.arrow_forward,
                              size: 14,
                              color: Colors.white,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 16),
              // Second column: Animated Progress indicator
              Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  AnimatedBuilder(
                    animation: Listenable.merge([
                      _fadeController,
                      _progressController,
                    ]),
                    builder: (context, child) {
                      return FadeTransition(
                        opacity: _fadeAnimation,
                        child: ScaleTransition(
                          scale: _scaleAnimation,
                          child: Stack(
                            alignment: Alignment.center,
                            children: [
                              SizedBox(
                                width: 75,
                                height: 75,
                                child: CircularProgressIndicator(
                                  value: _progressAnimation.value,
                                  strokeWidth: 6,
                                  backgroundColor: Colors.white.withValues(
                                    alpha: 0.3,
                                  ),
                                  valueColor:
                                      const AlwaysStoppedAnimation<Color>(
                                        Colors.white,
                                      ),
                                ),
                              ),
                              AnimatedBuilder(
                                animation: _counterAnimation,
                                builder: (context, child) {
                                  return AppText(
                                    '${_counterAnimation.value}%',
                                    fontSize: 14,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.white,
                                  );
                                },
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAnimatedSectionHeader(String title, int position) {
    return AnimationConfiguration.staggeredList(
      position: position,
      duration: const Duration(milliseconds: 350),
      child: SlideAnimation(
        horizontalOffset: -20.0,
        curve: Curves.easeOutCubic,
        child: FadeInAnimation(
          curve: Curves.easeOutCubic,
          child: _buildSectionHeader(title),
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return AppText(
      title,
      fontSize: 16,
      fontWeight: FontWeight.w600,
      color: Colors.black54,
    );
  }

  Widget _buildAnimatedMenuTile({
    required int position,
    required IconData icon,
    required Color iconColor,
    required String title,
    String? badge,
    required VoidCallback onTap,
  }) {
    return AnimationConfiguration.staggeredList(
      position: position,
      duration: const Duration(milliseconds: 300),
      child: SlideAnimation(
        verticalOffset: 20.0,
        curve: Curves.easeOutCubic,
        child: FadeInAnimation(
          curve: Curves.easeOutCubic,
          child: _buildMenuTile(
            icon: icon,
            iconColor: iconColor,
            title: title,
            badge: badge,
            onTap: onTap,
          ),
        ),
      ),
    );
  }

  Widget _buildMenuTile({
    required IconData icon,
    required Color iconColor,
    required String title,
    String? badge,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: iconColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: iconColor, size: 20),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: AppText(
                title,
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ),
            if (badge != null) ...[
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.red,
                  borderRadius: BorderRadius.circular(10),
                ),
                child: AppText(
                  badge,
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const SizedBox(width: 8),
            ],
            const Icon(
              Icons.arrow_forward_ios,
              size: 14,
              color: Colors.black54,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFloatingHelpButton() {
    return SafeArea(
      child: Container(
        decoration: BoxDecoration(
          color: const Color(0xFF2D3748),
          borderRadius: BorderRadius.circular(25),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.3),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: BorderRadius.circular(25),
            onTap: () {},
            child: const Padding(
              padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.help_outline, color: Colors.white, size: 20),
                  SizedBox(width: 8),
                  AppText(
                    'Help',
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Colors.white,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
